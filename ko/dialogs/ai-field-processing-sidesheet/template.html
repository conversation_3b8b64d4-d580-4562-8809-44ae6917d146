<sidesheet params="ref: modal, dialogWrapper: $component">
  <div class="foquz-dialog__header">
    <div class="container">
      <h2 class="foquz-dialog__title" data-bind="text: title"></h2>
    </div>
  </div>

  <div class="foquz-dialog__body ai-field-processing-sidesheet__body">
    <div class="foquz-dialog__scroll" data-bind="nativeScrollbar">
      <div class="container pb-4" data-tooltip-container>
    <!-- Sentiment -->
    <!-- ko if: showSentiment -->
    <div class="ai-field-processing-sidesheet__section">
      <fc-switch params="checked: sentimentEnabled, disabled: readonly, label: 'Определение тональности с помощью ИИ'"></fc-switch>

      <!-- ko template: { foreach: templateIf(sentimentEnabled(), $data), afterAdd: slideAfterAddFactory(300), beforeRemove: slideBeforeRemoveFactory(200) } -->
      <div class="ai-field-processing-sidesheet__block">
        <div class="ai-field-processing-sidesheet__block-inner">
          <div class="form-group">
          <label class="form-label" for="sentiment-prompt-textarea">Промт для определения тональности</label>

          <div class="ai-field-processing-sidesheet__hint">
            Используйте в промте 3 типа тональности: негативный, нейтральный и позитивный.
          </div>

          <div class="ai-field-processing-sidesheet__textarea" id="sentiment-prompt">
            <copy-textarea params="value: sentimentPrompt, disabled: readonly, maxlength: maxSentimentPromptLength, isInvalid: sentimentInvalid, onCopied: sentimentPromptCopied, tooltip: 'Скопировать в буфер'"></copy-textarea>
          </div>
          <validation-feedback params="show: $component.formControlErrorStateMatcher(sentimentPrompt), text: sentimentPrompt.error"></validation-feedback>
          <fc-status-popper params="target: 'sentiment-prompt', show: sentimentPromptCopied, mode: 'success'">Промт скопирован в буфер</fc-status-popper>
          </div>
        </div>
      </div>
      <!-- /ko -->
    </div>
    <!-- /ko -->

    <!-- Tagging -->
    <!-- ko if: showTagging -->
    <div class="ai-field-processing-sidesheet__section">
      <fc-switch params="checked: taggingEnabled, disabled: readonly, label: 'Тегирование с помощью ИИ'"></fc-switch>

      <!-- ko template: { foreach: templateIf(taggingEnabled(), $data), afterAdd: slideAfterAddFactory(300), beforeRemove: slideBeforeRemoveFactory(200) } -->
      <div class="ai-field-processing-sidesheet__block">
        <div class="ai-field-processing-sidesheet__block-inner">
          <div class="ai-field-processing-sidesheet__hint">
          Используйте в промте выбранные теги, чтобы ИИ автоматически присваивал их ответам респондентов.
        </div>

        <div class="form-group">
          <label class="form-label">Теги</label>
          <div class="ai-field-processing-sidesheet__tags">
            <!-- ko if: directories.tags.loaded -->
            <div data-bind="
              component: {
                name: 'clients-tag-input',
                params: {
                  afterAddTag: function() { directories.tags.load(true) },
                  value: tags,
                  list: directories.tags.data,
                  question_id: ext.questionId,
                  ai_field_id: ext.aiFieldId
                }
              }
            "></div>
            <!-- /ko -->
            <!-- ko if: isSubmitted() && (!tags() || !tags().length) -->
            <div class="form-error">Выберите хотя бы один тег</div>
            <!-- /ko -->
          </div>
        </div>

        <div class="form-group">
          <label class="form-label" for="tagging-prompt-textarea">Промт для тегирования</label>
          <div class="ai-field-processing-sidesheet__textarea" id="tagging-prompt">
            <copy-textarea params="value: taggingPrompt, disabled: readonly, maxlength: maxTaggingPromptLength, isInvalid: taggingInvalid, onCopied: taggingPromptCopied, tooltip: 'Скопировать в буфер'"></copy-textarea>
          </div>
          <validation-feedback params="show: $component.formControlErrorStateMatcher(taggingPrompt), text: taggingPrompt.error"></validation-feedback>
          <fc-status-popper params="target: 'tagging-prompt', show: taggingPromptCopied, mode: 'success'">Промт скопирован в буфер</fc-status-popper>
          </div>
        </div>
      </div>
      <!-- /ko -->
    </div>
    <!-- /ko -->
      </div>
    </div>
  </div>

  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <button type="button" class="f-btn f-btn-link" data-bind="click: function() { cancel() }">Отменить</button>
      <button type="button" class="f-btn" data-bind="click: function() { apply() }">Применить</button>
    </div>
  </div>
</sidesheet>
